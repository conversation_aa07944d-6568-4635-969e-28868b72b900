<template>
  <div class="web-crawler">
    <div class="input-section">
      <h2>输入要爬取的网页URL</h2>
      <div class="input-group">
        <input
          v-model="url"
          type="url"
          placeholder="请输入网页URL，例如：https://example.com"
          class="url-input"
          :disabled="isLoading"
          @keyup.enter="crawlWebsite"
        />
        <button
          @click="crawlWebsite"
          :disabled="!url || isLoading"
          class="crawl-button"
        >
          {{ isLoading ? '爬取中...' : '开始爬取' }}
        </button>
      </div>
    </div>

    <div v-if="status" class="status-section">
      <div :class="['status-message', statusType]">
        {{ status }}
      </div>
    </div>

    <div v-if="progress" class="progress-section">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }"></div>
      </div>
      <p class="progress-text">{{ progress }}% 完成</p>
    </div>

    <div v-if="result" class="result-section">
      <h3>爬取结果</h3>
      <div class="result-info">
        <p><strong>URL:</strong> {{ result.url }}</p>
        <p><strong>标题:</strong> {{ result.title }}</p>
        <p><strong>保存路径:</strong> {{ result.filePath }}</p>
        <p><strong>文件大小:</strong> {{ result.fileSize }}</p>
        <p><strong>爬取时间:</strong> {{ result.timestamp }}</p>
      </div>

      <!-- 视频检测结果 -->
      <div v-if="result.videos && result.videos.length > 0" class="video-section">
        <h4>检测到的视频文件 ({{ result.videos.length }}个)</h4>
        <div class="video-list">
          <div v-for="(video, index) in result.videos" :key="index" class="video-item">
            <div class="video-info">
              <p class="video-url">{{ video.url }}</p>
              <p class="video-source">来源: {{ video.source }}</p>
            </div>
            <button
              @click="downloadVideo(video.url, index)"
              :disabled="downloadingVideos[index]"
              class="download-video-btn"
            >
              {{ downloadingVideos[index] ? '下载中...' : '下载视频' }}
            </button>
          </div>
        </div>
      </div>

      <div class="content-preview">
        <h4>内容预览 (前500字符)</h4>
        <pre class="preview-text">{{ result.preview }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { crawlWebsiteWithFirecrawl, saveContentToFile, detectVideoLinks, downloadVideo } from '../utils/crawler'

const url = ref('')
const isLoading = ref(false)
const status = ref('')
const statusType = ref('')
const progress = ref(0)
const result = ref(null)
const downloadingVideos = ref({})

const crawlWebsite = async () => {
  if (!url.value) {
    setStatus('请输入有效的URL地址', 'error')
    return
  }

  try {
    isLoading.value = true
    result.value = null
    progress.value = 0
    
    setStatus('正在验证URL...', 'info')
    progress.value = 10
    
    // 验证URL格式
    const urlPattern = /^https?:\/\/.+/
    if (!urlPattern.test(url.value)) {
      throw new Error('请输入有效的HTTP或HTTPS URL')
    }
    
    setStatus('正在调用Firecrawl MCP进行网页爬取...', 'info')
    progress.value = 30
    
    // 调用Firecrawl MCP爬取网页
    const crawledData = await crawlWebsiteWithFirecrawl(url.value)
    progress.value = 60

    setStatus('正在检测视频文件...', 'info')

    // 检测视频链接
    const videoLinks = detectVideoLinks(crawledData.htmlContent || '', url.value)
    progress.value = 70

    setStatus('正在保存文件到本地...', 'info')

    // 保存到本地文件
    const saveResult = await saveContentToFile(crawledData, url.value)
    progress.value = 100

    // 设置结果
    result.value = {
      url: url.value,
      title: crawledData.title || '未知标题',
      filePath: saveResult.filePath,
      fileSize: saveResult.fileSize,
      timestamp: new Date().toLocaleString('zh-CN'),
      preview: crawledData.content ? crawledData.content.substring(0, 500) + '...' : '无内容预览',
      videos: videoLinks
    }
    
    setStatus('网页爬取完成！', 'success')
    
  } catch (error) {
    console.error('爬取失败:', error)
    setStatus(`爬取失败: ${error.message}`, 'error')
    progress.value = 0
  } finally {
    isLoading.value = false
  }
}

const setStatus = (message, type) => {
  status.value = message
  statusType.value = type

  // 3秒后清除状态消息（除了错误消息）
  if (type !== 'error') {
    setTimeout(() => {
      if (status.value === message) {
        status.value = ''
        statusType.value = ''
      }
    }, 3000)
  }
}

const downloadVideo = async (videoUrl, index) => {
  try {
    downloadingVideos.value[index] = true

    // 生成文件名
    const urlObj = new URL(videoUrl)
    const pathname = urlObj.pathname
    const filename = pathname.split('/').pop() || `video_${index + 1}.mp4`

    setStatus(`正在下载视频: ${filename}`, 'info')

    const downloadResult = await downloadVideo(videoUrl, filename)

    if (downloadResult.success) {
      setStatus(`视频下载完成: ${filename}`, 'success')
    }

  } catch (error) {
    console.error('视频下载失败:', error)
    setStatus(`视频下载失败: ${error.message}`, 'error')
  } finally {
    downloadingVideos.value[index] = false
  }
}
</script>

<style scoped>
.web-crawler {
  max-width: 800px;
  margin: 0 auto;
}

.input-section {
  margin-bottom: 2rem;
}

.input-section h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.input-group {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.url-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.url-input:focus {
  outline: none;
  border-color: #3498db;
}

.url-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.crawl-button {
  padding: 0.75rem 1.5rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  white-space: nowrap;
}

.crawl-button:hover:not(:disabled) {
  background-color: #2980b9;
}

.crawl-button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.status-section {
  margin-bottom: 1.5rem;
}

.status-message {
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-weight: 500;
}

.status-message.info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.status-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.progress-section {
  margin-bottom: 1.5rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background-color: #28a745;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0;
}

.result-section {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 1.5rem;
}

.result-section h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.result-info {
  margin-bottom: 1.5rem;
}

.result-info p {
  margin: 0.5rem 0;
  color: #495057;
}

.result-info strong {
  color: #2c3e50;
}

.content-preview h4 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.preview-text {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  font-size: 0.9rem;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .crawl-button {
    margin-top: 0.5rem;
  }
}
</style>
