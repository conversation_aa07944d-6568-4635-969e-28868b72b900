{"name": "default-browser-id", "version": "5.0.0", "description": "Get the bundle identifier of the default browser (macOS). Example: com.apple.Safari", "license": "MIT", "repository": "sindresorhus/default-browser-id", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["macos", "browser", "default", "plist", "web", "bundle", "bundleid", "id", "identifier", "uti"], "devDependencies": {"ava": "^6.0.1", "xo": "^0.56.0"}}