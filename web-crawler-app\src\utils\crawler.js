import axios from 'axios'
import firecrawlMCP from './firecrawl-mcp.js'

/**
 * 使用Firecrawl MCP爬取网页内容
 * @param {string} url - 要爬取的网页URL
 * @returns {Promise<Object>} 爬取的网页数据
 */
export async function crawlWebsiteWithFirecrawl(url) {
  try {
    console.log('正在调用Firecrawl MCP爬取:', url)

    // 尝试调用真正的Firecrawl MCP服务
    try {
      const firecrawlResponse = await firecrawlMCP.scrape(url, {
        formats: ['markdown', 'html'],
        onlyMainContent: true,
        waitFor: 1000
      })
      return firecrawlResponse
    } catch (mcpError) {
      console.warn('Firecrawl MCP调用失败，使用备用方案:', mcpError.message)

      // 备用方案：直接HTTP请求
      const response = await axios.get(url, {
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      })

      const htmlContent = response.data

      // 提取标题
      const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i)
      const title = titleMatch ? titleMatch[1].trim() : '未知标题'

      // 简单的内容提取（移除HTML标签）
      const textContent = htmlContent
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
        .replace(/<[^>]+>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim()

      return {
        url: url,
        title: title,
        content: textContent,
        htmlContent: htmlContent,
        timestamp: new Date().toISOString(),
        contentLength: textContent.length,
        htmlLength: htmlContent.length,
        source: 'fallback'
      }
    }

  } catch (error) {
    console.error('网页爬取失败:', error)

    if (error.code === 'ENOTFOUND') {
      throw new Error('无法连接到指定的URL，请检查网址是否正确')
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('请求超时，请稍后重试')
    } else if (error.response) {
      throw new Error(`服务器返回错误: ${error.response.status} ${error.response.statusText}`)
    } else {
      throw new Error(`网络错误: ${error.message}`)
    }
  }
}

/**
 * 调用Firecrawl MCP服务
 * @param {string} url - 要爬取的URL
 * @returns {Promise<Object>} MCP响应数据
 */
async function callFirecrawlMCP(url) {
  // 这里应该是真正的Firecrawl MCP调用
  // 由于在浏览器环境中无法直接调用MCP，这里提供一个模拟实现

  // 在实际部署中，你需要：
  // 1. 创建一个后端API端点来调用Firecrawl MCP
  // 2. 或者使用WebSocket连接到MCP服务
  // 3. 或者使用其他适合的通信方式

  const mcpEndpoint = '/api/firecrawl-mcp' // 假设的后端端点

  try {
    const response = await axios.post(mcpEndpoint, {
      action: 'scrape',
      url: url,
      formats: ['markdown', 'html'],
      onlyMainContent: true
    })

    return {
      url: url,
      title: response.data.title || '未知标题',
      content: response.data.markdown || response.data.content || '',
      htmlContent: response.data.html || response.data.rawHtml || '',
      timestamp: new Date().toISOString(),
      contentLength: (response.data.markdown || response.data.content || '').length,
      htmlLength: (response.data.html || response.data.rawHtml || '').length,
      source: 'firecrawl-mcp'
    }
  } catch (error) {
    throw new Error(`MCP服务调用失败: ${error.message}`)
  }
}

/**
 * 将爬取的内容保存到本地文件
 * @param {Object} crawledData - 爬取的数据
 * @param {string} originalUrl - 原始URL
 * @returns {Promise<Object>} 保存结果
 */
export async function saveContentToFile(crawledData, originalUrl) {
  try {
    // 生成文件名
    const urlObj = new URL(originalUrl)
    const domain = urlObj.hostname.replace(/[^a-zA-Z0-9]/g, '_')
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const fileName = `crawled_${domain}_${timestamp}`
    
    // 准备要保存的数据
    const dataToSave = {
      metadata: {
        originalUrl: originalUrl,
        title: crawledData.title,
        crawledAt: crawledData.timestamp,
        contentLength: crawledData.contentLength,
        htmlLength: crawledData.htmlLength
      },
      content: {
        text: crawledData.content,
        html: crawledData.htmlContent
      }
    }
    
    // 在浏览器环境中，我们使用Blob和下载链接来保存文件
    const jsonContent = JSON.stringify(dataToSave, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json' })
    
    // 创建下载链接
    const downloadUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = `${fileName}.json`
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 清理URL对象
    URL.revokeObjectURL(downloadUrl)
    
    // 同时保存纯文本版本
    const textBlob = new Blob([crawledData.content], { type: 'text/plain' })
    const textDownloadUrl = URL.createObjectURL(textBlob)
    const textLink = document.createElement('a')
    textLink.href = textDownloadUrl
    textLink.download = `${fileName}.txt`
    
    document.body.appendChild(textLink)
    textLink.click()
    document.body.removeChild(textLink)
    
    URL.revokeObjectURL(textDownloadUrl)
    
    return {
      filePath: `${fileName}.json 和 ${fileName}.txt`,
      fileSize: formatFileSize(jsonContent.length + crawledData.content.length),
      success: true
    }
    
  } catch (error) {
    console.error('保存文件失败:', error)
    throw new Error(`保存文件失败: ${error.message}`)
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化的文件大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 验证URL格式
 * @param {string} url - 要验证的URL
 * @returns {boolean} 是否为有效URL
 */
export function isValidUrl(url) {
  try {
    new URL(url)
    return url.startsWith('http://') || url.startsWith('https://')
  } catch {
    return false
  }
}

/**
 * 清理文件名中的非法字符
 * @param {string} filename - 原始文件名
 * @returns {string} 清理后的文件名
 */
export function sanitizeFilename(filename) {
  return filename.replace(/[<>:"/\\|?*]/g, '_').replace(/\s+/g, '_')
}
