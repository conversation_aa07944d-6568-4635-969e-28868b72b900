import * as _vue_compiler_sfc from '@vue/compiler-sfc';
export { SimpleTypeResolveOptions as Options } from '@vue/compiler-sfc';
import * as BabelCore from '@babel/core';

declare const _default: (api: object, options: Partial<Pick<_vue_compiler_sfc.SFCScriptCompileOptions, "globalTypeFiles" | "fs" | "babelParserPlugins" | "isProd">> | null | undefined, dirname: string) => BabelCore.PluginObj<BabelCore.PluginPass>;

export { _default as default };
