{"name": "is-unicode-supported", "version": "2.1.0", "description": "Detect whether the terminal supports Unicode", "license": "MIT", "repository": "sindresorhus/is-unicode-supported", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["terminal", "unicode", "detect", "utf8", "console", "shell", "support", "supports", "supported", "check", "detection"], "devDependencies": {"ava": "^6.1.3", "tsd": "^0.31.2", "xo": "^0.59.3"}}