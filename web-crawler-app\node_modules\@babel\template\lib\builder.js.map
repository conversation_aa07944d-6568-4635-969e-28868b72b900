{"version": 3, "names": ["_options", "require", "_string", "_literal", "NO_PLACEHOLDER", "validate", "placeholder<PERSON><PERSON><PERSON>", "createTemplateBuilder", "formatter", "defaultOpts", "templateFnCache", "WeakMap", "templateAstCache", "cachedOpts", "Object", "assign", "tpl", "args", "length", "Error", "extendedTrace", "stringTemplate", "merge", "Array", "isArray", "builder", "get", "literalTemplate", "set", "ast", "fn", "rootStack", "error", "stack", "split", "slice", "join", "arg", "err"], "sources": ["../src/builder.ts"], "sourcesContent": ["import { merge, validate } from \"./options.ts\";\nimport type {\n  TemplateOpts,\n  PublicOpts,\n  PublicReplacements,\n} from \"./options.ts\";\nimport type { Formatter } from \"./formatters.ts\";\n\nimport stringTemplate from \"./string.ts\";\nimport literalTemplate from \"./literal.ts\";\n\nexport type TemplateBuilder<T> = {\n  // Build a new builder, merging the given options with the previous ones.\n  (opts: PublicOpts): TemplateBuilder<T>;\n\n  // Building from a string produces an AST builder function by default.\n  (tpl: string, opts?: PublicOpts): (replacements?: PublicReplacements) => T;\n\n  // Building from a template literal produces an AST builder function by default.\n  (\n    tpl: TemplateStringsArray,\n    ...args: Array<unknown>\n  ): (replacements?: PublicReplacements) => T;\n\n  // Allow users to explicitly create templates that produce ASTs, skipping\n  // the need for an intermediate function.\n  ast: {\n    (tpl: string, opts?: PublicOpts): T;\n    (tpl: TemplateStringsArray, ...args: Array<unknown>): T;\n  };\n};\n\n// Prebuild the options that will be used when parsing a `.ast` template.\n// These do not use a pattern because there is no way for users to pass in\n// replacement patterns to begin with, and disabling pattern matching means\n// users have more flexibility in what type of content they have in their\n// template JS.\nconst NO_PLACEHOLDER: TemplateOpts = validate({\n  placeholderPattern: false,\n});\n\nexport default function createTemplateBuilder<T>(\n  formatter: Formatter<T>,\n  defaultOpts?: TemplateOpts,\n): TemplateBuilder<T> {\n  const templateFnCache = new WeakMap();\n  const templateAstCache = new WeakMap();\n  const cachedOpts = defaultOpts || validate(null);\n\n  return Object.assign(\n    ((tpl, ...args) => {\n      if (typeof tpl === \"string\") {\n        if (args.length > 1) throw new Error(\"Unexpected extra params.\");\n        return extendedTrace(\n          stringTemplate(formatter, tpl, merge(cachedOpts, validate(args[0]))),\n        );\n      } else if (Array.isArray(tpl)) {\n        let builder = templateFnCache.get(tpl);\n        if (!builder) {\n          builder = literalTemplate(formatter, tpl, cachedOpts);\n          templateFnCache.set(tpl, builder);\n        }\n        return extendedTrace(builder(args));\n      } else if (typeof tpl === \"object\" && tpl) {\n        if (args.length > 0) throw new Error(\"Unexpected extra params.\");\n        return createTemplateBuilder(\n          formatter,\n          merge(cachedOpts, validate(tpl)),\n        );\n      }\n      throw new Error(`Unexpected template param ${typeof tpl}`);\n    }) as TemplateBuilder<T>,\n    {\n      ast: (tpl: string | Array<string>, ...args: Array<unknown>) => {\n        if (typeof tpl === \"string\") {\n          if (args.length > 1) throw new Error(\"Unexpected extra params.\");\n          return stringTemplate(\n            formatter,\n            tpl,\n            merge(merge(cachedOpts, validate(args[0])), NO_PLACEHOLDER),\n          )();\n        } else if (Array.isArray(tpl)) {\n          let builder = templateAstCache.get(tpl);\n          if (!builder) {\n            builder = literalTemplate(\n              formatter,\n              tpl,\n              merge(cachedOpts, NO_PLACEHOLDER),\n            );\n            templateAstCache.set(tpl, builder);\n          }\n          return builder(args)();\n        }\n\n        throw new Error(`Unexpected template param ${typeof tpl}`);\n      },\n    },\n  );\n}\n\nfunction extendedTrace<Arg, Result>(\n  fn: (_: Arg) => Result,\n): (_: Arg) => Result {\n  // Since we lazy parse the template, we get the current stack so we have the\n  // original stack to append if it errors when parsing\n  let rootStack = \"\";\n  try {\n    // error stack gets populated in IE only on throw\n    // (https://msdn.microsoft.com/en-us/library/hh699850(v=vs.94).aspx)\n    throw new Error();\n  } catch (error) {\n    if (error.stack) {\n      // error.stack does not exists in IE <= 9\n      // We slice off the top 3 items in the stack to remove the call to\n      // 'extendedTrace', and the anonymous builder function, with the final\n      // stripped line being the error message itself since we threw it\n      // in the first place and it doesn't matter.\n      rootStack = error.stack.split(\"\\n\").slice(3).join(\"\\n\");\n    }\n  }\n\n  return (arg: Arg) => {\n    try {\n      return fn(arg);\n    } catch (err) {\n      err.stack += `\\n    =============\\n${rootStack}`;\n      throw err;\n    }\n  };\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAQA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AA4BA,MAAMG,cAA4B,GAAG,IAAAC,iBAAQ,EAAC;EAC5CC,kBAAkB,EAAE;AACtB,CAAC,CAAC;AAEa,SAASC,qBAAqBA,CAC3CC,SAAuB,EACvBC,WAA0B,EACN;EACpB,MAAMC,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;EACrC,MAAMC,gBAAgB,GAAG,IAAID,OAAO,CAAC,CAAC;EACtC,MAAME,UAAU,GAAGJ,WAAW,IAAI,IAAAJ,iBAAQ,EAAC,IAAI,CAAC;EAEhD,OAAOS,MAAM,CAACC,MAAM,CACjB,CAACC,GAAG,EAAE,GAAGC,IAAI,KAAK;IACjB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MAC3B,IAAIC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;MAChE,OAAOC,aAAa,CAClB,IAAAC,eAAc,EAACb,SAAS,EAAEQ,GAAG,EAAE,IAAAM,cAAK,EAACT,UAAU,EAAE,IAAAR,iBAAQ,EAACY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACrE,CAAC;IACH,CAAC,MAAM,IAAIM,KAAK,CAACC,OAAO,CAACR,GAAG,CAAC,EAAE;MAC7B,IAAIS,OAAO,GAAGf,eAAe,CAACgB,GAAG,CAACV,GAAG,CAAC;MACtC,IAAI,CAACS,OAAO,EAAE;QACZA,OAAO,GAAG,IAAAE,gBAAe,EAACnB,SAAS,EAAEQ,GAAG,EAAEH,UAAU,CAAC;QACrDH,eAAe,CAACkB,GAAG,CAACZ,GAAG,EAAES,OAAO,CAAC;MACnC;MACA,OAAOL,aAAa,CAACK,OAAO,CAACR,IAAI,CAAC,CAAC;IACrC,CAAC,MAAM,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,EAAE;MACzC,IAAIC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;MAChE,OAAOZ,qBAAqB,CAC1BC,SAAS,EACT,IAAAc,cAAK,EAACT,UAAU,EAAE,IAAAR,iBAAQ,EAACW,GAAG,CAAC,CACjC,CAAC;IACH;IACA,MAAM,IAAIG,KAAK,CAAC,6BAA6B,OAAOH,GAAG,EAAE,CAAC;EAC5D,CAAC,EACD;IACEa,GAAG,EAAEA,CAACb,GAA2B,EAAE,GAAGC,IAAoB,KAAK;MAC7D,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;QAC3B,IAAIC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;QAChE,OAAO,IAAAE,eAAc,EACnBb,SAAS,EACTQ,GAAG,EACH,IAAAM,cAAK,EAAC,IAAAA,cAAK,EAACT,UAAU,EAAE,IAAAR,iBAAQ,EAACY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEb,cAAc,CAC5D,CAAC,CAAC,CAAC;MACL,CAAC,MAAM,IAAImB,KAAK,CAACC,OAAO,CAACR,GAAG,CAAC,EAAE;QAC7B,IAAIS,OAAO,GAAGb,gBAAgB,CAACc,GAAG,CAACV,GAAG,CAAC;QACvC,IAAI,CAACS,OAAO,EAAE;UACZA,OAAO,GAAG,IAAAE,gBAAe,EACvBnB,SAAS,EACTQ,GAAG,EACH,IAAAM,cAAK,EAACT,UAAU,EAAET,cAAc,CAClC,CAAC;UACDQ,gBAAgB,CAACgB,GAAG,CAACZ,GAAG,EAAES,OAAO,CAAC;QACpC;QACA,OAAOA,OAAO,CAACR,IAAI,CAAC,CAAC,CAAC;MACxB;MAEA,MAAM,IAAIE,KAAK,CAAC,6BAA6B,OAAOH,GAAG,EAAE,CAAC;IAC5D;EACF,CACF,CAAC;AACH;AAEA,SAASI,aAAaA,CACpBU,EAAsB,EACF;EAGpB,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAI;IAGF,MAAM,IAAIZ,KAAK,CAAC,CAAC;EACnB,CAAC,CAAC,OAAOa,KAAK,EAAE;IACd,IAAIA,KAAK,CAACC,KAAK,EAAE;MAMfF,SAAS,GAAGC,KAAK,CAACC,KAAK,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACzD;EACF;EAEA,OAAQC,GAAQ,IAAK;IACnB,IAAI;MACF,OAAOP,EAAE,CAACO,GAAG,CAAC;IAChB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZA,GAAG,CAACL,KAAK,IAAI,wBAAwBF,SAAS,EAAE;MAChD,MAAMO,GAAG;IACX;EACF,CAAC;AACH", "ignoreList": []}