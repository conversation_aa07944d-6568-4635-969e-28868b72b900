@echo off
echo ================================
echo    网页爬取工具启动脚本
echo ================================
echo.

echo 正在检查依赖...
if not exist "node_modules" (
    echo 依赖未安装，正在安装...
    npm install
    if errorlevel 1 (
        echo 依赖安装失败！
        pause
        exit /b 1
    )
)

echo 正在编译项目...
npm run build
if errorlevel 1 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 编译完成！正在启动服务器...
echo 服务器将在 http://localhost:3001 启动
echo.
echo 按 Ctrl+C 停止服务器
echo ================================
echo.

npm run server
