<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="./favicon.svg" type="image/svg+xml">
  <title>Vite Inspect</title>
  <script type="module" crossorigin src="./assets/index-B8p3M0dz.js"></script>
  <link rel="modulepreload" crossorigin href="./assets/runtime-core.esm-bundler-De4U-ag6.js">
  <link rel="modulepreload" crossorigin href="./assets/payload-D299S_JB.js">
  <link rel="modulepreload" crossorigin href="./assets/vue-router-BQnQzYAn.js">
  <link rel="stylesheet" crossorigin href="./assets/index-Dd_dmTvN.css">
</head>
<body data-vite-inspect-mode="DEV" class="bg-main text-main font-sans">
  <div id="app"></div>
  <script>
    (function () {
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
      const setting = localStorage.getItem('color-schema') || 'auto'
      if (setting === 'dark' || (prefersDark && setting !== 'light'))
        document.documentElement.classList.toggle('dark', true)
    })()

    ;(function () {
      if (!location.pathname.endsWith('/'))
        location.pathname += '/'
    })()
  </script>
</body>
</html>
