<script setup>
import { ref } from 'vue'
import WebCrawler from './components/WebCrawler.vue'

</script>

<template>
  <div id="app">
    <header class="app-header">
      <h1>网页爬取工具</h1>
      <p>使用 Firecrawl MCP 爬取网页内容并保存到本地</p>
    </header>

    <main class="app-main">
      <WebCrawler />
    </main>
  </div>
</template>

<style scoped>
#app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  text-align: center;
  margin-bottom: 3rem;
}

.app-header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.app-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.app-main {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
</style>
