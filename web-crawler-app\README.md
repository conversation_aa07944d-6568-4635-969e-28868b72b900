# 网页爬取工具

一个基于Vue.js和Firecrawl MCP的网页爬取应用，可以爬取网页内容并保存到本地。

## 功能特性

- 🌐 支持任意HTTP/HTTPS网页爬取
- 🔥 集成Firecrawl MCP进行高质量内容提取
- 📄 支持多种格式输出（Markdown、HTML、纯文本）
- 💾 自动保存爬取内容到本地文件
- 📊 实时显示爬取进度
- 🎨 现代化的用户界面
- 📱 响应式设计，支持移动端

## 技术栈

- **前端**: Vue.js 3 + Vite
- **后端**: Express.js
- **爬取引擎**: Firecrawl MCP
- **样式**: CSS3 + 响应式设计
- **HTTP客户端**: Axios

## 项目结构

```
web-crawler-app/
├── src/
│   ├── components/
│   │   └── WebCrawler.vue      # 主要爬取组件
│   ├── utils/
│   │   ├── crawler.js          # 爬取工具函数
│   │   └── firecrawl-mcp.js    # Firecrawl MCP客户端
│   ├── App.vue                 # 根组件
│   └── main.js                 # 应用入口
├── server.js                   # Express服务器
├── dist/                       # 编译输出目录
└── package.json               # 项目配置
```

## 安装和使用

### 1. 安装依赖

```bash
npm install
```

### 2. 编译项目

```bash
npm run build
```

### 3. 启动服务器

```bash
npm run server
```

或者使用组合命令：

```bash
npm start
```

### 4. 访问应用

打开浏览器访问: `http://localhost:3001`

## 使用说明

1. **输入URL**: 在输入框中输入要爬取的网页URL
2. **开始爬取**: 点击"开始爬取"按钮
3. **查看进度**: 实时查看爬取进度条
4. **下载文件**: 爬取完成后，文件会自动下载到本地
