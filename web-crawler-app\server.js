import express from 'express'
import cors from 'cors'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const app = express()
const PORT = process.env.PORT || 3001

// 中间件
app.use(cors())
app.use(express.json())
app.use(express.static(path.join(__dirname, 'dist')))

/**
 * 模拟Firecrawl MCP调用的API端点
 * 在实际应用中，这里应该调用真正的Firecrawl MCP服务
 */
app.post('/api/firecrawl-mcp', async (req, res) => {
  try {
    const { url, action, formats, onlyMainContent } = req.body
    
    console.log('收到Firecrawl MCP请求:', { url, action, formats, onlyMainContent })
    
    if (!url) {
      return res.status(400).json({ error: 'URL是必需的' })
    }
    
    // 这里应该调用真正的Firecrawl MCP
    // 由于这是演示，我们使用一个模拟的响应
    
    // 在实际应用中，你需要：
    // 1. 安装并配置Firecrawl MCP
    // 2. 使用适当的MCP客户端库
    // 3. 调用相应的MCP方法
    
    // 模拟的Firecrawl MCP响应
    const mockResponse = await simulateFirecrawlMCP(url, { action, formats, onlyMainContent })
    
    res.json(mockResponse)
    
  } catch (error) {
    console.error('Firecrawl MCP API错误:', error)
    res.status(500).json({ 
      error: 'Firecrawl MCP调用失败', 
      details: error.message 
    })
  }
})

/**
 * 模拟Firecrawl MCP调用
 * @param {string} url - 要爬取的URL
 * @param {Object} options - 爬取选项
 * @returns {Promise<Object>} 模拟的响应数据
 */
async function simulateFirecrawlMCP(url, options) {
  // 这是一个模拟实现
  // 在实际应用中，这里应该是真正的Firecrawl MCP调用
  
  try {
    // 使用fetch获取网页内容（Node.js 18+支持）
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const htmlContent = await response.text()
    
    // 提取标题
    const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i)
    const title = titleMatch ? titleMatch[1].trim() : '未知标题'
    
    // 提取主要内容（简单实现）
    let textContent = htmlContent
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<nav[^>]*>[\s\S]*?<\/nav>/gi, '')
      .replace(/<header[^>]*>[\s\S]*?<\/header>/gi, '')
      .replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi, '')
      .replace(/<aside[^>]*>[\s\S]*?<\/aside>/gi, '')
      .replace(/<[^>]+>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
    
    // 如果只需要主要内容，尝试提取文章内容
    if (options.onlyMainContent) {
      const mainContentMatch = htmlContent.match(/<main[^>]*>([\s\S]*?)<\/main>/i) ||
                              htmlContent.match(/<article[^>]*>([\s\S]*?)<\/article>/i) ||
                              htmlContent.match(/<div[^>]*class="[^"]*content[^"]*"[^>]*>([\s\S]*?)<\/div>/i)
      
      if (mainContentMatch) {
        textContent = mainContentMatch[1]
          .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
          .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
          .replace(/<[^>]+>/g, ' ')
          .replace(/\s+/g, ' ')
          .trim()
      }
    }
    
    // 转换为Markdown格式（简单实现）
    let markdownContent = textContent
    
    // 简单的HTML到Markdown转换
    if (options.formats && options.formats.includes('markdown')) {
      markdownContent = htmlContent
        .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
        .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
        .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
        .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '#### $1\n\n')
        .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '##### $1\n\n')
        .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '###### $1\n\n')
        .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
        .replace(/<br\s*\/?>/gi, '\n')
        .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
        .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
        .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
        .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')
        .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
        .replace(/<[^>]+>/g, '')
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        .trim()
    }
    
    return {
      title: title,
      content: textContent,
      markdown: markdownContent,
      html: htmlContent,
      rawHtml: htmlContent,
      url: url,
      timestamp: new Date().toISOString(),
      success: true
    }
    
  } catch (error) {
    throw new Error(`模拟Firecrawl MCP失败: ${error.message}`)
  }
}

// 处理根路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'))
})

app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`)
  console.log('Firecrawl MCP API端点: /api/firecrawl-mcp')
})
