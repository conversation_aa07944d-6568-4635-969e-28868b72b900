{"name": "vite-dev-rpc", "type": "module", "version": "1.1.0", "description": "Remote procedure call for client-server communication in Vite plugins", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/vite-dev-rpc#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/vite-dev-rpc.git"}, "bugs": {"url": "https://github.com/antfu/vite-dev-rpc/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "peerDependencies": {"vite": "^2.9.0 || ^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.1 || ^7.0.0-0"}, "dependencies": {"birpc": "^2.4.0", "vite-hot-client": "^2.1.0"}, "devDependencies": {"@antfu/eslint-config": "^4.16.1", "@antfu/ni": "^25.0.0", "@babel/types": "^7.27.7", "@types/node": "^24.0.7", "bumpp": "^10.2.0", "eslint": "^9.30.0", "esno": "^4.8.0", "pnpm": "^10.12.4", "rimraf": "^6.0.1", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vite": "^7.0.0", "vitest": "^3.2.4"}, "scripts": {"build": "rimraf dist && unbuild", "dev": "unbuild --stub", "lint": "eslint .", "play": "nr dev && vite playground", "release": "bumpp && pnpm publish", "start": "esno src/index.ts", "test": "vitest", "typecheck": "tsc --noEmit"}}