/**
 * Firecrawl MCP客户端
 * 这个文件包含了与Firecrawl MCP服务交互的所有功能
 */

/**
 * Firecrawl MCP客户端类
 */
class FirecrawlMCPClient {
  constructor() {
    this.baseUrl = '/api/firecrawl-mcp'
  }

  /**
   * 爬取单个网页
   * @param {string} url - 要爬取的URL
   * @param {Object} options - 爬取选项
   * @returns {Promise<Object>} 爬取结果
   */
  async scrape(url, options = {}) {
    const defaultOptions = {
      formats: ['markdown', 'html'],
      onlyMainContent: true,
      waitFor: 0,
      timeout: 30000
    }

    const scrapeOptions = { ...defaultOptions, ...options }

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'scrape',
          url: url,
          ...scrapeOptions
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return this.processScrapedData(data, url)

    } catch (error) {
      console.error('Firecrawl MCP爬取失败:', error)
      throw new Error(`爬取失败: ${error.message}`)
    }
  }

  /**
   * 批量爬取多个网页
   * @param {Array<string>} urls - URL数组
   * @param {Object} options - 爬取选项
   * @returns {Promise<Array>} 爬取结果数组
   */
  async batchScrape(urls, options = {}) {
    const results = []
    
    for (const url of urls) {
      try {
        const result = await this.scrape(url, options)
        results.push(result)
      } catch (error) {
        results.push({
          url: url,
          error: error.message,
          success: false
        })
      }
    }

    return results
  }

  /**
   * 搜索网页内容
   * @param {string} query - 搜索查询
   * @param {Object} options - 搜索选项
   * @returns {Promise<Object>} 搜索结果
   */
  async search(query, options = {}) {
    const defaultOptions = {
      limit: 5,
      lang: 'zh',
      country: 'cn'
    }

    const searchOptions = { ...defaultOptions, ...options }

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'search',
          query: query,
          ...searchOptions
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Firecrawl MCP搜索失败:', error)
      throw new Error(`搜索失败: ${error.message}`)
    }
  }

  /**
   * 爬取网站地图
   * @param {string} url - 网站URL
   * @param {Object} options - 爬取选项
   * @returns {Promise<Object>} 网站地图结果
   */
  async map(url, options = {}) {
    const defaultOptions = {
      limit: 100,
      includeSubdomains: false
    }

    const mapOptions = { ...defaultOptions, ...options }

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'map',
          url: url,
          ...mapOptions
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Firecrawl MCP地图爬取失败:', error)
      throw new Error(`地图爬取失败: ${error.message}`)
    }
  }

  /**
   * 处理爬取的数据
   * @param {Object} data - 原始数据
   * @param {string} url - 原始URL
   * @returns {Object} 处理后的数据
   */
  processScrapedData(data, url) {
    return {
      url: url,
      title: data.title || '未知标题',
      content: data.content || data.markdown || '',
      markdown: data.markdown || '',
      html: data.html || data.rawHtml || '',
      timestamp: data.timestamp || new Date().toISOString(),
      contentLength: (data.content || data.markdown || '').length,
      htmlLength: (data.html || data.rawHtml || '').length,
      success: data.success !== false,
      source: 'firecrawl-mcp'
    }
  }

  /**
   * 验证URL格式
   * @param {string} url - 要验证的URL
   * @returns {boolean} 是否为有效URL
   */
  isValidUrl(url) {
    try {
      new URL(url)
      return url.startsWith('http://') || url.startsWith('https://')
    } catch {
      return false
    }
  }

  /**
   * 获取域名
   * @param {string} url - URL
   * @returns {string} 域名
   */
  getDomain(url) {
    try {
      return new URL(url).hostname
    } catch {
      return 'unknown'
    }
  }
}

// 创建单例实例
const firecrawlMCP = new FirecrawlMCPClient()

export default firecrawlMCP

// 导出常用方法
export const { scrape, batchScrape, search, map } = firecrawlMCP
